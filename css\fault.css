/* 故障页面图表区域样式 */
.fault-charts {
  margin: 20px 0;
}

.chart-row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10px;
}

.faultchart-container {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  flex: 1;
  min-width: 300px;
  padding: 0 10px;
  margin-bottom: 20px;
}

.faultchart-container h3 {
  text-align: center;
  margin-bottom: 10px;
}

@media (max-width: 992px) {
  .faultchart-container {
    flex: 0 0 100%;
    max-width: 100%;
  }
}

.search-btn,
.reset-btn,
.download-btn,
.add-btn {
    padding: 6px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.search-btn,
.download-btn,
.add-btn {
    background-color: #4c6fff;
    color: white;
}

.reset-btn {
    background-color: #6c757d;
    color: white;
}

.search-btn:hover,
.download-btn:hover,
.add-btn:hover {
    background-color: #3a5ae8;
}

.reset-btn:hover {
    background-color: #5a6268;
}

.button-group {
    display: flex;
    gap: 10px;
    margin-left: auto; 
}

/* 故障操作按钮样式 */
.fault-action-buttons {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
}

.fault-action-btn {
  width: 70px;
  height: 30px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  background-color: #007bff;
  color: white;
}

.fault-action-btn:hover {
  background-color: #0056b3;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.fault-action-btn-danger {
  background-color: #dc3545;
}

.fault-action-btn-danger:hover {
  background-color: #c82333;
}

/* 加载弹框样式 */
.loading-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
}

.loading-modal.show {
  opacity: 1;
  visibility: visible;
}

.loading-modal-content {
  background: white;
  padding: 40px;
  border-radius: 12px;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  min-width: 300px;
  max-width: 400px;
}

/* 加载动画 */
.loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #4c6fff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-message {
  font-size: 16px;
  color: #333;
  font-weight: 500;
}

/* 成功提示样式 */
.success-icon {
  width: 60px;
  height: 60px;
  background-color: #28a745;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30px;
  font-weight: bold;
  margin: 0 auto 20px;
  animation: successPulse 0.6s ease-out;
}

@keyframes successPulse {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.success-message {
  font-size: 16px;
  color: #333;
  margin-bottom: 25px;
  font-weight: 500;
}

.success-confirm-btn {
  background-color: #4c6fff;
  color: white;
  border: none;
  padding: 10px 30px;
  border-radius: 6px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.success-confirm-btn:hover {
  background-color: #3a5ae8;
}

/* 提交按钮禁用状态 */
.btn-submit:disabled {
  background-color: #6c757d !important;
  cursor: not-allowed !important;
  opacity: 0.6;
}

.fault-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
}

.fault-modal-header h2 {
    margin: 0;
    font-size: 24px;
    color: #333;
}

.fault-modal-content {
    position: relative;
    background-color: #eee;
    margin: 50px auto;
    padding: 20px;
    width: 90%;
    max-width: 1200px;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.fault-form-container {
    max-width: 1500px;
  display: flex;
  flex-wrap: wrap;
  margin: 20px auto;
    padding: 20px;
  background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* 模态框注册表单样式 */
#faultRegisterModal .modal-content {
  max-width: 1400px;
  width: 95%;
  max-height: 90vh;
  overflow-y: auto;
}

#faultRegisterModal .form-container {
  display: flex;
  gap: 30px;
  margin-bottom: 20px;
}

#faultRegisterModal .left-column,
#faultRegisterModal .right-column {
  flex: 1;
}

#faultRegisterModal .form-group {
  margin-bottom: 15px;
}

#faultRegisterModal .form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #333;
}

#faultRegisterModal .form-group input,
#faultRegisterModal .form-group select,
#faultRegisterModal .form-group textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  box-sizing: border-box;
}

#faultRegisterModal .form-group textarea {
  resize: vertical;
  min-height: 80px;
}

#faultRegisterModal .upload-area {
  border: 2px dashed #ddd;
  border-radius: 4px;
  padding: 20px;
  text-align: center;
  background-color: #fafafa;
}

#faultRegisterModal .file-list {
  margin-top: 10px;
  text-align: left;
}

#faultRegisterModal .file-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  margin-bottom: 5px;
}

#faultRegisterModal .file-name {
  cursor: pointer;
  color: #007bff;
  text-decoration: underline;
  flex: 1;
}

#faultRegisterModal .file-size {
  color: #6c757d;
  font-size: 12px;
  margin: 0 10px;
}

#faultRegisterModal .btn-delete-file {
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  cursor: pointer;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

#faultRegisterModal .btn-delete-file:hover {
  background: #c82333;
}

#faultRegisterModal .modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

#faultRegisterModal .btn-cancel,
#faultRegisterModal .btn-submit {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
}

#faultRegisterModal .btn-cancel {
  background-color: #6c757d;
  color: white;
}

#faultRegisterModal .btn-cancel:hover {
  background-color: #5a6268;
}

#faultRegisterModal .btn-submit {
  background-color: #4c6fff;
  color: white;
}

#faultRegisterModal .btn-submit:hover {
  background-color: #3a5ae8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  #faultRegisterModal .form-container {
    flex-direction: column;
    gap: 20px;
  }

  #faultRegisterModal .modal-content {
    width: 98%;
    margin: 20px auto;
    padding: 15px;
  }
}